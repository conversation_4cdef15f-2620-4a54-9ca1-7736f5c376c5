package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestStudentTestMarksIntegrationFlow(t *testing.T) {
	t.Skip("Skipping integration test")
	// Use timestamp to ensure unique names across test runs
	timestamp := fmt.Sprintf("%d", time.Now().UnixNano())
	courseName := "TestCourse_" + timestamp
	testName := "TestMark_" + timestamp
	subjectName := "TestSubject_" + timestamp
	sectionTypeName := "TestSection_" + timestamp
	testTypeName := "TestType_" + timestamp
	chapterName := "TestChapter_" + timestamp
	topicName := "TestTopic_" + timestamp
	difficultyName := "TestDifficulty_" + timestamp

	// Clean up before test
	defer func() {
		db.Exec("DELETE FROM student_test_marks WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM test_responses WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM sections_questions WHERE section_id IN (SELECT id FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?))", testName)
		db.Exec("DELETE FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM courses_tests WHERE course_id IN (SELECT id FROM courses WHERE name = ?)", courseName)
		db.Exec("DELETE FROM tests WHERE name = ?", testName)
		db.Exec("DELETE FROM test_type_section_types WHERE test_type_id IN (SELECT id FROM test_types WHERE name = ?)", testTypeName)
		db.Exec("DELETE FROM test_types WHERE name = ?", testTypeName)
		db.Exec("DELETE FROM section_types WHERE name = ?", sectionTypeName)
		db.Exec("DELETE FROM options WHERE question_id IN (SELECT id FROM questions WHERE text LIKE ?)", "%"+timestamp+"%")
		db.Exec("DELETE FROM questions WHERE text LIKE ?", "%"+timestamp+"%")
		db.Exec("DELETE FROM topics WHERE name = ?", topicName)
		db.Exec("DELETE FROM chapters WHERE name = ?", chapterName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
		db.Exec("DELETE FROM difficulties WHERE name = ?", difficultyName)
		db.Exec("DELETE FROM courses WHERE name = ?", courseName)
	}()

	// Step 1: Create all required entities
	// Create subject
	subject := models.Subject{
		Name:        subjectName,
		DisplayName: subjectName,
	}
	subjectResp := requestExecutionHelper(http.MethodPost, "/api/subjects", subject)
	assert.Equal(t, http.StatusOK, subjectResp.Code)

	// Create chapter
	chapter := models.ChapterForCreate{
		Name:        chapterName,
		DisplayName: chapterName,
		SubjectName: subjectName,
	}
	chapterResp := requestExecutionHelper(http.MethodPost, "/api/chapters", chapter)
	assert.Equal(t, http.StatusOK, chapterResp.Code)

	// Create topic
	topic := models.TopicForCreate{
		Name:        topicName,
		ChapterName: chapterName,
	}
	topicResp := requestExecutionHelper(http.MethodPost, "/api/topics", topic)
	assert.Equal(t, http.StatusOK, topicResp.Code)

	// Create difficulty
	difficulty := models.Difficulty{Name: difficultyName}
	db.Create(&difficulty)

	// Create section type with specific positive/negative marks
	sectionType := models.SectionTypeForCreate{
		Name:          sectionTypeName,
		SubjectName:   subjectName,
		QuestionCount: 2,
		PositiveMarks: 4.0, // +4 for correct answers
		NegativeMarks: 1.0, // -1 for incorrect answers
	}
	sectionTypeResp := requestExecutionHelper(http.MethodPost, "/api/section-types", sectionType)
	assert.Equal(t, http.StatusOK, sectionTypeResp.Code)

	// Create test type
	testType := models.TestTypeForCreate{
		Name:             testTypeName,
		SectionTypeNames: []string{sectionTypeName},
	}
	testTypeResp := requestExecutionHelper(http.MethodPost, "/api/test-types", testType)
	assert.Equal(t, http.StatusOK, testTypeResp.Code)

	// Create test
	test := models.TestForCreate{
		Name:         testName,
		TestTypeName: testTypeName,
		Description:  "Integration test for StudentTestMark creation",
		FromTime:     time.Now(),
		ToTime:       time.Now().Add(2 * time.Hour),
	}
	testResp := requestExecutionHelper(http.MethodPost, "/api/tests", test)
	assert.Equal(t, http.StatusOK, testResp.Code)

	var createdTest models.Test
	err := json.Unmarshal(testResp.Body.Bytes(), &createdTest)
	assert.Nil(t, err)

	// Create MCQ question with options
	mcqQuestion := models.QuestionForCreate{
		Text:           "What is 2+2? " + timestamp,
		TopicName:      topicName,
		DifficultyName: difficultyName,
		QuestionType:   "mcq",
		Options: []models.OptionForCreate{
			{OptionText: "3", IsCorrect: false},
			{OptionText: "4", IsCorrect: true},
			{OptionText: "5", IsCorrect: false},
		},
	}
	mcqResp := requestExecutionHelper(http.MethodPost, "/api/questions", mcqQuestion)
	assert.Equal(t, http.StatusOK, mcqResp.Code)

	var createdMCQQuestion models.SimpleEntityResponse
	err = json.Unmarshal(mcqResp.Body.Bytes(), &createdMCQQuestion)
	assert.Nil(t, err)

	// Create text question
	textQuestion := models.QuestionForCreate{
		Text:           "What is the capital of France? " + timestamp,
		TopicName:      topicName,
		DifficultyName: difficultyName,
		QuestionType:   "text",
		CorrectAnswer:  "Paris",
	}
	textResp := requestExecutionHelper(http.MethodPost, "/api/questions", textQuestion)
	assert.Equal(t, http.StatusOK, textResp.Code)

	var createdTextQuestion models.SimpleEntityResponse
	err = json.Unmarshal(textResp.Body.Bytes(), &createdTextQuestion)
	assert.Nil(t, err)

	// Add questions to test
	addQuestionsRequest := models.AddQuestionsToTestRequest{
		SectionName: sectionTypeName,
		QuestionIDs: []uint{createdMCQQuestion.ID, createdTextQuestion.ID},
	}
	addQuestionsResp := requestExecutionHelper(http.MethodPost, fmt.Sprintf("/api/tests/%d/questions", createdTest.ID), addQuestionsRequest)
	assert.Equal(t, http.StatusOK, addQuestionsResp.Code)

	// Step 2: Create a student and record test responses
	student := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Test Student " + timestamp,
			Email:          "teststudent" + timestamp + "@example.com",
			PhoneNumber:    "1234567890",
			ContactAddress: "Test Address",
		},
		ParentPhone: "9876543210",
		ParentEmail: "parent" + timestamp + "@example.com",
	}
	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", student)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var createdStudentResponse models.CreatedStudentResponse
	err = json.Unmarshal(studentResp.Body.Bytes(), &createdStudentResponse)
	assert.Nil(t, err)

	// Record test responses (1 correct MCQ, 1 incorrect text)
	testResponses := models.TestResponsesForCreate{
		TestID: createdTest.ID,
		Responses: []models.TestResponseForCreate{
			{
				QuestionID:        createdMCQQuestion.ID,
				SelectedOptionIDs: []int{}, // We need to get the correct option ID
				ResponseText:      nil,
			},
			{
				QuestionID:        createdTextQuestion.ID,
				SelectedOptionIDs: []int{},
				ResponseText:      func() *string { s := "London"; return &s }(), // Incorrect answer
			},
		},
	}

	// Get the correct option ID for MCQ
	var mcqQuestionFromDB models.Question
	db.Preload("Options").First(&mcqQuestionFromDB, createdMCQQuestion.ID)
	for _, option := range mcqQuestionFromDB.Options {
		if option.IsCorrect {
			testResponses.Responses[0].SelectedOptionIDs = []int{int(option.ID)}
			break
		}
	}

	// We can use the token from student creation response directly
	// The CreateStudent API already returns a token
	loginToken := createdStudentResponse.Token

	// Record responses with authentication
	recordResp := authenticatedRequestHelper(http.MethodPost, "/api/test-responses", testResponses, loginToken)
	assert.Equal(t, http.StatusOK, recordResp.Code)

	// Step 3: Evaluate test responses
	evaluationRequest := models.TestEvaluationRequest{
		TestID: createdTest.ID,
	}
	evalResp := authenticatedRequestHelper(http.MethodPost, "/api/test-responses/evaluate", evaluationRequest, loginToken)
	assert.Equal(t, http.StatusOK, evalResp.Code)

	var evaluationResult models.TestEvaluationResult
	err = json.Unmarshal(evalResp.Body.Bytes(), &evaluationResult)
	assert.Nil(t, err)

	// Verify evaluation results
	assert.Equal(t, 1, evaluationResult.TotalStudentsEvaluated)
	assert.Equal(t, 1, len(evaluationResult.StudentResults))

	studentResult := evaluationResult.StudentResults[0]
	assert.Equal(t, createdStudentResponse.Student.ID, studentResult.StudentID)
	assert.Equal(t, 2, studentResult.TotalQuestions)
	assert.Equal(t, 1, studentResult.CorrectAnswers) // 1 correct MCQ
	assert.Equal(t, 3, studentResult.TotalScore)     // +4 for correct MCQ, -1 for incorrect text = 3

	// Step 4: Verify StudentTestMark record was created
	var studentTestMark models.StudentTestMark
	err = db.Where("student_id = ? AND test_id = ?", createdStudentResponse.Student.ID, createdTest.ID).First(&studentTestMark).Error
	assert.Nil(t, err, "StudentTestMark record should be created")

	assert.Equal(t, int(createdStudentResponse.Student.ID), studentTestMark.StudentID)
	assert.Equal(t, int(createdTest.ID), studentTestMark.TestID)
	assert.Equal(t, 4, studentTestMark.TotalPositiveMarks) // +4 for correct MCQ
	assert.Equal(t, 1, studentTestMark.TotalNegativeMarks) // +1 for incorrect text (stored as positive)
	// Final marks should be calculated by database as positive - negative = 4 - 1 = 3

	// Step 5: Test GetTestRankings API
	rankingsResp := authenticatedRequestHelper(http.MethodGet, fmt.Sprintf("/api/test-responses/rankings/%d", createdTest.ID), nil, loginToken)
	assert.Equal(t, http.StatusOK, rankingsResp.Code)

	var rankingsResult models.TestRankingResult
	err = json.Unmarshal(rankingsResp.Body.Bytes(), &rankingsResult)
	assert.Nil(t, err)

	// Verify rankings
	assert.Equal(t, createdTest.ID, rankingsResult.TestID)
	assert.Equal(t, testName, rankingsResult.TestName)
	assert.Equal(t, 1, rankingsResult.TotalStudents)
	assert.Equal(t, 1, len(rankingsResult.StudentRankings))

	ranking := rankingsResult.StudentRankings[0]
	assert.Equal(t, createdStudentResponse.Student.ID, ranking.StudentID)
	assert.Equal(t, 4, ranking.TotalPositiveMarks)
	assert.Equal(t, 1, ranking.TotalNegativeMarks)
	assert.Equal(t, 3, ranking.FinalMarks)     // 4 - 1 = 3
	assert.Equal(t, 1, ranking.Rank)           // Should be rank 1
	assert.Equal(t, 100.0, ranking.Percentile) // 100% since only one student

	t.Logf("✅ Integration test passed! StudentTestMark created with positive=%d, negative=%d, final=%d",
		studentTestMark.TotalPositiveMarks, studentTestMark.TotalNegativeMarks, ranking.FinalMarks)
}
