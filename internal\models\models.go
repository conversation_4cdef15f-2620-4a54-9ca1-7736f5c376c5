package models

import (
	"strings"
	"time"

	"gorm.io/gorm"
)

type User struct {
	gorm.Model
	FullName       string `gorm:"not null"`
	Email          string `gorm:"not null;unique_index"`
	PhoneNumber    string `gorm:"not null;unique_index"`
	PasswordHash   string
	Role           string
	ContactAddress string
	EmailVerified  bool
	PhoneVerified  bool
}

type UserForCreate struct {
	FullName       string
	Email          string
	PhoneNumber    string
	ContactAddress string
}

type Student struct {
	gorm.Model
	ParentPhone string `json:"parent_phone"`
	ParentEmail string `json:"parent_email"`
	UserID      uint
	User        User
	Courses     []Course `gorm:"many2many:students_courses;"`
	Institute   string   `json:"institute"`
	Class       string   `json:"class" gorm:"check:class IN ('9th', '10th', '11th', '12th', 'dropper')"`
	Stream      string   `json:"stream" gorm:"check:stream IN ('IIT-JEE', 'NEET')"`
	CityOrTown  string   `json:"city_or_town"`
	State       string   `json:"state"`
}

type StudentForCreate struct {
	UserForCreate
	ParentPhone string `json:"parent_phone"`
	ParentEmail string `json:"parent_email"`
	Institute   string `json:"institute"`
	Class       string `json:"class" binding:"omitempty,oneof=9th 10th 11th 12th dropper"`
	Stream      string `json:"stream" binding:"omitempty,oneof=IIT-JEE NEET"`
	CityOrTown  string `json:"city_or_town"`
	State       string `json:"state"`
}

type StudentForList struct {
	ID          uint      `json:"id"`
	FullName    string    `json:"full_name"`
	Email       string    `json:"email"`
	PhoneNumber string    `json:"phone_number"`
	ParentPhone string    `json:"parent_phone"`
	ParentEmail string    `json:"parent_email"`
	Institute   string    `json:"institute"`
	Class       string    `json:"class"`
	Stream      string    `json:"stream"`
	CityOrTown  string    `json:"city_or_town"`
	State       string    `json:"state"`
	CreatedAt   time.Time `json:"created_at"`
}

type Video struct {
	gorm.Model
	Name        string
	DisplayName string
	VideoUrl    string
	ViewCount   uint
	ChapterID   uint
}

type VideoForGet struct {
	Name        string    `json:"name"`
	DisplayName string    `json:"display_name"`
	VideoUrl    string    `json:"video_url"`
	ViewCount   uint      `json:"view_count"`
	ChapterID   uint      `json:"chapter_id"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type VideoForCreate struct {
	Name        string
	DisplayName string
	VideoUrl    string
	ChapterName string
}

type StudyMaterial struct {
	gorm.Model
	Name        string
	DisplayName string
	Url         string
	ChapterID   uint
}

type StudyMaterialForGet struct {
	Name        string    `json:"name"`
	DisplayName string    `json:"display_name"`
	Url         string    `json:"url"`
	ChapterID   uint      `json:"chapter_id"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type MaterialForCreate struct {
	Name        string
	DisplayName string
	Url         string
	ChapterName string
}

type Content struct {
	Videos []VideoForGet         `json:"videos"`
	Pdfs   []StudyMaterialForGet `json:"pdfs"`
}

type Chapter struct {
	gorm.Model
	Name           string `gorm:"unique;not null"`
	DisplayName    string
	Videos         []Video
	StudyMaterials []StudyMaterial
	SubjectID      uint
	Subject        Subject `gorm:"foreignKey:SubjectID"`
}

type ChapterForCreate struct {
	Name        string
	DisplayName string
	SubjectName string
}

type Subject struct {
	gorm.Model
	Name        string `gorm:"unique;not null"`
	DisplayName string
	Chapters    []Chapter
}

type SubjectForCreate struct {
	Name        string
	DisplayName string
}

type SubjectsForCreate struct {
	Subjects []SubjectForCreate `json:"subjects"`
}

type CourseForCreate struct {
	Name           string
	Description    string
	Price          int
	Discount       float32
	DurationInDays int
	IsFree         bool
	CourseType     string
	SubjectNames   []string `json:"subjectNames"`
}

type Course struct {
	gorm.Model
	Name           string `gorm:"unique;not null"`
	Description    string
	Price          int
	Discount       float32
	DurationInDays int
	IsFree         bool      `gorm:"not null;default:false"`
	CourseType     string    `gorm:"not null;check:course_type IN ('IIT-JEE', 'NEET')"`
	Subjects       []Subject `gorm:"many2many:courses_subjects;"`
	Tests          []Test    `gorm:"many2many:courses_tests;"`
}

type CourseWithPurchased struct {
	Name           string
	Description    string
	Price          int
	Discount       float32
	DurationInDays int
	IsFree         bool
	CourseType     string
	Purchased      bool
}

// CoursesByCategory organizes courses by free/paid status and course type
type CoursesByCategory struct {
	FreeCourses []CoursesByType `json:"free_courses"`
	PaidCourses []CoursesByType `json:"paid_courses"`
}

// CoursesByType organizes courses by course type
type CoursesByType struct {
	CourseType string                `json:"course_type"`
	Courses    []CourseWithPurchased `json:"courses"`
}

// ChapterDetails represents chapter information with videos and study materials for course details
type ChapterDetails struct {
	ID             uint                  `json:"id"`
	Name           string                `json:"name"`
	DisplayName    string                `json:"display_name"`
	Videos         []VideoForGet         `json:"videos"`
	StudyMaterials []StudyMaterialForGet `json:"study_materials"`
}

// SubjectDetails represents subject information with chapters for course details
type SubjectDetails struct {
	ID          uint             `json:"id"`
	Name        string           `json:"name"`
	DisplayName string           `json:"display_name"`
	Chapters    []ChapterDetails `json:"chapters"`
}

// CourseDetails represents detailed course information including subjects, chapters, videos, and study materials
type CourseDetails struct {
	ID             uint             `json:"id"`
	Name           string           `json:"name"`
	Description    string           `json:"description"`
	Price          int              `json:"price"`
	Discount       float32          `json:"discount"`
	DurationInDays int              `json:"duration_in_days"`
	IsFree         bool             `json:"is_free"`
	CourseType     string           `json:"course_type"`
	Subjects       []SubjectDetails `json:"subjects"`
	CreatedAt      time.Time        `json:"created_at"`
	UpdatedAt      time.Time        `json:"updated_at"`
}

type Credentials struct {
	UserEmail string `json:"user_email"`
	Password  string `json:"password"`
}

type UpdatePassword struct {
	NewPassword string `json:"new_password"`
}

type AdminForCreate struct {
	FullName       string `json:"full_name" binding:"required"`
	Email          string `json:"email" binding:"required,email"`
	PhoneNumber    string `json:"phone_number" binding:"required"`
	ContactAddress string `json:"contact_address"`
	Password       string `json:"password" binding:"required,min=6"`
}

func (us *User) Sanitize() {
	us.FullName = strings.TrimSpace(us.FullName)
	us.Email = strings.TrimSpace(us.Email)
	us.PhoneNumber = strings.TrimSpace(us.PhoneNumber)
	us.ContactAddress = strings.TrimSpace(us.ContactAddress)
}

type Difficulty struct {
	gorm.Model
	Name      string `gorm:"unique;not null"`
	Questions []Question
}

type Topic struct {
	gorm.Model
	Name         string  `gorm:"unique;not null"`
	ChapterID    uint    `gorm:"not null"`
	Chapter      Chapter `gorm:"foreignKey:ChapterID"`
	Questions    []Question
	FormulaCards []FormulaCard
}

type TopicForCreate struct {
	Name        string
	ChapterName string
}

type Question struct {
	gorm.Model
	Text          string     `gorm:"not null"`
	TopicID       uint       `gorm:"not null"`
	DifficultyID  uint       `gorm:"not null"`
	Topic         Topic      `gorm:"foreignKey:TopicID"`
	Difficulty    Difficulty `gorm:"foreignKey:DifficultyID"`
	QuestionType  string
	ImageUrl      string
	FileUrl       string
	CorrectAnswer string
	Options       []Option `gorm:"foreignKey:QuestionID"` // One-to-many relationship with options
}

type OptionForCreate struct {
	OptionText     string  `json:"option_text" binding:"required"`
	OptionImageURL *string `json:"option_image_url,omitempty"`
	IsCorrect      bool    `json:"is_correct"`
}

type QuestionForCreate struct {
	Text           string            `json:"text" binding:"required"`
	TopicName      string            `json:"topic_name" binding:"required"`
	DifficultyName string            `json:"difficulty_name" binding:"required"`
	QuestionType   string            `json:"question_type" binding:"required"`
	ImageUrl       string            `json:"image_url,omitempty"`
	FileUrl        string            `json:"file_url,omitempty"`
	CorrectAnswer  string            `json:"correct_answer,omitempty"`
	Options        []OptionForCreate `json:"options,omitempty"`
}

type SectionType struct {
	gorm.Model
	Name          string `gorm:"unique;not null"`
	SubjectID     uint   `gorm:"not null"`
	Subject       Subject
	QuestionCount int     `gorm:"not null"`
	PositiveMarks float64 `gorm:"not null"`
	NegativeMarks float64 `gorm:"not null"`
}

type SectionTypeForCreate struct {
	Name          string
	SubjectName   string
	QuestionCount int
	PositiveMarks float64
	NegativeMarks float64
}

type TestType struct {
	ID           uint          `gorm:"primaryKey"`
	Name         string        `gorm:"unique;not null"`
	SectionTypes []SectionType `gorm:"many2many:test_type_section_types;"`
	CreatedAt    time.Time
	UpdatedAt    time.Time
}

type TestTypeForCreate struct {
	Name             string
	SectionTypeNames []string
}

// EvaluationStatus represents the evaluation status of a test
type EvaluationStatus string

const (
	EvaluationStatusNotApplicable EvaluationStatus = "NotApplicable" // Test not yet attempted by any student
	EvaluationStatusPending       EvaluationStatus = "Pending"       // Some students have responses but not evaluated
	EvaluationStatusEvaluated     EvaluationStatus = "Evaluated"     // All student responses are evaluated
)

type Test struct {
	gorm.Model
	Name             string `gorm:"not null"`
	TestTypeID       uint   `gorm:"not null"`
	TestType         TestType
	Sections         []Section
	FromTime         time.Time
	ToTime           time.Time
	Active           bool
	Description      string
	EvaluationStatus EvaluationStatus `gorm:"type:varchar(20);default:'NotApplicable'"`
}

type TestForCreate struct {
	Name         string
	TestTypeName string
	Description  string
	FromTime     time.Time
	ToTime       time.Time
}

type TestForGet struct {
	ID               uint
	Name             string
	TestType         string
	SectionNames     []string
	FromTime         time.Time
	ToTime           time.Time
	Active           bool
	Description      string
	EvaluationStatus EvaluationStatus
}

type Section struct {
	gorm.Model
	Name          string     `gorm:"unique not null"`
	DisplayName   string     `gorm:"not null"`
	TestID        uint       `gorm:"not null"`
	Test          Test       `gorm:"foreignKey:TestID"`
	Questions     []Question `gorm:"many2many:sections_questions;"`
	SectionTypeID uint       `gorm:"not null"`
	SectionType   SectionType
}

type SectionForCreate struct {
	Name            string
	DisplayName     string
	SectionTypeName string
}

type Option struct {
	gorm.Model

	QuestionID     uint     `gorm:"not null"`              // Foreign key to Questions
	Question       Question `gorm:"foreignKey:QuestionID"` // GORM association
	OptionText     string   `gorm:"type:text;not null"`    // Option content
	OptionImageURL *string  `gorm:"type:text"`             // Optional image URL
	IsCorrect      bool     `gorm:"default:false"`         // Correctness flag
}

type TestResponse struct {
	gorm.Model

	StudentID uint    `gorm:"not null"`             // FK to students
	Student   Student `gorm:"foreignKey:StudentID"` // Assumes you have a Student model

	TestID uint `gorm:"not null"` // FK to tests
	Test   Test `gorm:"foreignKey:TestID"`

	QuestionID uint     `gorm:"not null"` // FK to questions
	Question   Question `gorm:"foreignKey:QuestionID"`

	SelectedOptionIDs []int   `gorm:"type:integer[]"` // PostgreSQL array type (use pgx or pq)
	ResponseText      *string `gorm:"type:text"`      // Nullable for text answers
	CalculatedScore   *int    `gorm:"default:null"`   // Nullable, can be set after evaluation
	IsCorrect         bool    `gorm:"default:false"`  // Automatically evaluated
}

type Response struct {
	gorm.Model

	CommentID uint    `gorm:"not null"` // FK to Comment
	Comment   Comment `gorm:"foreignKey:CommentID"`

	UserID uint `gorm:"not null"` // FK to User
	User   User `gorm:"foreignKey:UserID"`

	ResponseText string `gorm:"type:text;not null"`
}

type Comment struct {
	gorm.Model

	UserID uint `gorm:"not null"`          // FK to users
	User   User `gorm:"foreignKey:UserID"` // Assumes a User model exists

	VideoID *uint  `gorm:"default:null"` // Optional: comments can belong to a video
	Video   *Video `gorm:"foreignKey:VideoID"`

	MaterialID *uint          `gorm:"default:null"` // Optional: or to a study material
	Material   *StudyMaterial `gorm:"foreignKey:MaterialID"`

	CommentText string `gorm:"type:text;not null"`

	Responses []Response `gorm:"constraint:OnDelete:CASCADE"` // Replies to this comment
}

// CommentForCreate represents the input for creating a new comment
type CommentForCreate struct {
	CommentText string `json:"comment_text" binding:"required"`
	VideoID     *uint  `json:"video_id,omitempty"`
	MaterialID  *uint  `json:"material_id,omitempty"`
}

// ResponseForCreate represents the input for creating a response to a comment
type ResponseForCreate struct {
	CommentID    uint   `json:"comment_id" binding:"required"`
	ResponseText string `json:"response_text" binding:"required"`
}

// CommentWithResponses represents a comment with its responses for API output
type CommentWithResponses struct {
	ID          uint             `json:"id"`
	CommentText string           `json:"comment_text"`
	UserName    string           `json:"user_name"`
	UserID      uint             `json:"user_id"`
	VideoID     *uint            `json:"video_id,omitempty"`
	MaterialID  *uint            `json:"material_id,omitempty"`
	CreatedAt   time.Time        `json:"created_at"`
	Responses   []ResponseOutput `json:"responses"`
}

// ResponseOutput represents a response for API output
type ResponseOutput struct {
	ID           uint      `json:"id"`
	ResponseText string    `json:"response_text"`
	UserName     string    `json:"user_name"`
	UserID       uint      `json:"user_id"`
	CreatedAt    time.Time `json:"created_at"`
}

// CommentsResponse represents the response structure for getting comments
type CommentsResponse struct {
	VideoID    *uint                  `json:"video_id,omitempty"`
	MaterialID *uint                  `json:"material_id,omitempty"`
	Comments   []CommentWithResponses `json:"comments"`
}

type StudentTestMark struct {
	StudentID          int        `gorm:"primaryKey;not null"`
	TestID             int        `gorm:"primaryKey;not null;index:idx_test_final_marks_desc"`
	TotalPositiveMarks int        `gorm:"default:0;not null"`
	TotalNegativeMarks int        `gorm:"default:0;not null"`
	FinalMarks         int        `gorm:"->;type:integer;not null;index:idx_test_final_marks_desc,sort:desc"` // Read-only; generated column with test-specific descending index
	CreatedAt          time.Time  `gorm:"autoCreateTime"`
	UpdatedAt          time.Time  `gorm:"autoUpdateTime"`
	DeletedAt          *time.Time `gorm:"index"`

	Student Student `gorm:"foreignKey:StudentID;constraint:OnDelete:CASCADE"`
	Test    Test    `gorm:"foreignKey:TestID;constraint:OnDelete:CASCADE"`
}

type CreatedStudentResponse struct {
	Token   string               `json:"token"`
	Student SimpleEntityResponse `json:"student"`
}

// SimpleEntityResponse represents a simple response with only ID and name for created entities
type SimpleEntityResponse struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

// CreatedAdminResponse represents the response for admin creation with token and simple admin details
type CreatedAdminResponse struct {
	Token string               `json:"token"`
	Admin SimpleEntityResponse `json:"admin"`
}

// Institution represents an educational institution
type Institution struct {
	gorm.Model
	Name          string `gorm:"not null" json:"name"`
	CityOrTown    string `gorm:"not null" json:"city_or_town"`
	State         string `gorm:"not null" json:"state"`
	ContactName   string `gorm:"not null" json:"contact_name"`
	ContactNumber string `gorm:"not null" json:"contact_number"`
}

// InstitutionForCreate represents the input structure for creating an institution
type InstitutionForCreate struct {
	Name          string `json:"name" binding:"required"`
	CityOrTown    string `json:"city_or_town" binding:"required"`
	State         string `json:"state" binding:"required"`
	ContactName   string `json:"contact_name" binding:"required"`
	ContactNumber string `json:"contact_number" binding:"required"`
}

// InstitutionForUpdate represents the input structure for updating an institution
type InstitutionForUpdate struct {
	Name          string `json:"name" binding:"required"`
	CityOrTown    string `json:"city_or_town" binding:"required"`
	State         string `json:"state" binding:"required"`
	ContactName   string `json:"contact_name" binding:"required"`
	ContactNumber string `json:"contact_number" binding:"required"`
}

// TestResponseForCreate represents the input for recording a single question response
type TestResponseForCreate struct {
	QuestionID        uint    `json:"question_id" binding:"required"`
	SelectedOptionIDs []int   `json:"selected_option_ids,omitempty"`
	ResponseText      *string `json:"response_text,omitempty"`
}

// AddQuestionsToTestRequest represents the input for adding questions to a test with section specification
type AddQuestionsToTestRequest struct {
	QuestionIDs []uint `json:"question_ids" binding:"required"`
	SectionName string `json:"section_name" binding:"required"`
}

// RemoveQuestionsFromTestRequest represents the input for removing questions from a test with section specification
type RemoveQuestionsFromTestRequest struct {
	QuestionIDs []uint `json:"question_ids" binding:"required"`
	SectionName string `json:"section_name" binding:"required"`
}

// TestResponsesForCreate represents the input for recording responses to all questions in a test
type TestResponsesForCreate struct {
	TestID    uint                    `json:"test_id" binding:"required"`
	Responses []TestResponseForCreate `json:"responses" binding:"required,min=1"`
}

// TestResponseResult represents the result of recording a test response
type TestResponseResult struct {
	QuestionID      uint   `json:"question_id"`
	IsCorrect       bool   `json:"is_correct"`
	CalculatedScore *int   `json:"calculated_score"`
	Message         string `json:"message,omitempty"`
}

// TestResponsesResult represents the result of recording all test responses
type TestResponsesResult struct {
	TestID          uint                 `json:"test_id"`
	StudentID       uint                 `json:"student_id"`
	TotalQuestions  int                  `json:"total_questions"`
	CorrectAnswers  int                  `json:"correct_answers"`
	TotalScore      int                  `json:"total_score"`
	ResponseResults []TestResponseResult `json:"response_results"`
	Message         string               `json:"message"`
}

// TestEvaluationRequest represents the request to evaluate responses for a test
type TestEvaluationRequest struct {
	TestID uint `json:"test_id" binding:"required"`
}

// SendVerificationCodeRequest represents the request to send verification code to a student
type SendVerificationCodeRequest struct {
	PhoneNumber string `json:"phone_number" binding:"required"`
}

// SendVerificationCodeResponse represents the response after sending verification code
type SendVerificationCodeResponse struct {
	Message   string `json:"message"`
	CodeSent  bool   `json:"code_sent"`
	ExpiresAt string `json:"expires_at,omitempty"`
}

// VerifyCodeRequest represents the request to verify a code entered by student
type VerifyCodeRequest struct {
	PhoneNumber string `json:"phone_number" binding:"required"`
	Code        string `json:"code" binding:"required"`
}

// VerifyCodeResponse represents the response after verifying the code
type VerifyCodeResponse struct {
	Message  string `json:"message"`
	Verified bool   `json:"verified"`
	Status   string `json:"status,omitempty"`
}

// StudentEvaluationResult represents the evaluation result for a single student
type StudentEvaluationResult struct {
	StudentID      uint   `json:"student_id"`
	StudentName    string `json:"student_name"`
	TotalQuestions int    `json:"total_questions"`
	CorrectAnswers int    `json:"correct_answers"`
	TotalScore     int    `json:"total_score"`
	EvaluationTime string `json:"evaluation_time"`
	Message        string `json:"message"`
}

// TestEvaluationResult represents the result of evaluating all unevaluated responses for a test
type TestEvaluationResult struct {
	TestID                 uint                      `json:"test_id"`
	TestName               string                    `json:"test_name"`
	TotalStudentsEvaluated int                       `json:"total_students_evaluated"`
	StudentResults         []StudentEvaluationResult `json:"student_results"`
	Message                string                    `json:"message"`
}

// StudentTestResponsesResult represents the enhanced result for GetStudentTestResponses API
type StudentTestResponsesResult struct {
	TestID              uint           `json:"test_id"`
	TestName            string         `json:"test_name"`
	StudentID           uint           `json:"student_id"`
	StudentName         string         `json:"student_name"`
	TotalScore          int            `json:"total_score"`
	ResponsesRecordedAt time.Time      `json:"responses_recorded_at"`
	Responses           []TestResponse `json:"responses"`
	Message             string         `json:"message"`
}

// StudentRankingInfo represents a student's ranking information for a specific test
type StudentRankingInfo struct {
	StudentID          uint    `json:"student_id"`
	StudentName        string  `json:"student_name"`
	StudentEmail       string  `json:"student_email"`
	TotalPositiveMarks int     `json:"total_positive_marks"`
	TotalNegativeMarks int     `json:"total_negative_marks"`
	FinalMarks         int     `json:"final_marks"`
	Rank               int     `json:"rank"`
	Percentile         float64 `json:"percentile"`
}

// TestRankingResult represents the ranking results for a specific test
type TestRankingResult struct {
	TestID          uint                 `json:"test_id"`
	TestName        string               `json:"test_name"`
	TotalStudents   int                  `json:"total_students"`
	HighestMarks    int                  `json:"highest_marks"`
	LowestMarks     int                  `json:"lowest_marks"`
	AverageMarks    float64              `json:"average_marks"`
	StudentRankings []StudentRankingInfo `json:"student_rankings"`
	Message         string               `json:"message"`
}

// TestQuestionInfo represents a question within a test section
type TestQuestionInfo struct {
	ID             uint     `json:"id"`
	Text           string   `json:"text"`
	QuestionType   string   `json:"question_type"`
	ImageUrl       string   `json:"image_url,omitempty"`
	FileUrl        string   `json:"file_url,omitempty"`
	Options        []Option `json:"options,omitempty"`
	TopicName      string   `json:"topic_name"`
	DifficultyName string   `json:"difficulty_name"`
	SubjectName    string   `json:"subject_name"`
}

// TestSectionInfo represents a section within a test with its questions
type TestSectionInfo struct {
	SectionID   uint               `json:"section_id"`
	SectionName string             `json:"section_name"`
	DisplayName string             `json:"display_name"`
	Questions   []TestQuestionInfo `json:"questions"`
}

// TestQuestionsResult represents the result for GetTestQuestions API
type TestQuestionsResult struct {
	TestID   uint              `json:"test_id"`
	TestName string            `json:"test_name"`
	TestType string            `json:"test_type"`
	Sections []TestSectionInfo `json:"sections"`
	Message  string            `json:"message"`
}

// FormulaCard represents a formula card that belongs to a topic
type FormulaCard struct {
	gorm.Model
	Name     string `gorm:"not null"`
	ImageUrl string `gorm:"not null"`
	TopicID  uint   `gorm:"not null"`
	Topic    Topic  `gorm:"foreignKey:TopicID"`
}

// FormulaCardForCreate represents the input for creating a single formula card
type FormulaCardForCreate struct {
	Name     string `json:"name" binding:"required"`
	ImageUrl string `json:"image_url" binding:"required"`
}

// FormulaCardsForCreate represents the input for creating multiple formula cards
type FormulaCardsForCreate struct {
	SubjectName  string                 `json:"subject_name" binding:"required"`
	ChapterName  string                 `json:"chapter_name" binding:"required"`
	TopicName    string                 `json:"topic_name" binding:"required"`
	FormulaCards []FormulaCardForCreate `json:"formula_cards" binding:"required,min=1"`
}

// FormulaCardSummary represents a simplified formula card with only essential attributes
type FormulaCardSummary struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	SubjectName string `json:"subject_name"`
	ChapterName string `json:"chapter_name"`
	TopicName   string `json:"topic_name"`
	ImageUrl    string `json:"image_url"`
}

// FormulaCardsByTopic represents formula cards organized by topic with count
type FormulaCardsByTopic struct {
	TopicName    string               `json:"topic_name"`
	FormulaCards []FormulaCardSummary `json:"formula_cards"`
	Count        int                  `json:"count"`
}

// FormulaCardsByChapter represents formula cards organized by chapter with topics and counts
type FormulaCardsByChapter struct {
	ChapterName string                `json:"chapter_name"`
	Topics      []FormulaCardsByTopic `json:"topics"`
	Count       int                   `json:"count"`
}

// FormulaCardsBySubject represents formula cards organized by subject with chapters, topics and counts
type FormulaCardsBySubject struct {
	SubjectName string                  `json:"subject_name"`
	Chapters    []FormulaCardsByChapter `json:"chapters"`
	Count       int                     `json:"count"`
}

// Exam type constants for previous year papers and course types
const (
	ExamTypeIITJEE = "IIT-JEE"
	ExamTypeNEET   = "NEET"
)

// Course type constants (reusing exam type values)
const (
	CourseTypeIITJEE = ExamTypeIITJEE
	CourseTypeNEET   = ExamTypeNEET
)

// Student class constants
const (
	StudentClass9th     = "9th"
	StudentClass10th    = "10th"
	StudentClass11th    = "11th"
	StudentClass12th    = "12th"
	StudentClassDropper = "dropper"
)

// Student stream constants (reusing exam type values)
const (
	StudentStreamIIT  = ExamTypeIITJEE
	StudentStreamNEET = ExamTypeNEET
)

// Transaction status constants
const (
	TransactionStatusPending   = "PENDING"
	TransactionStatusCompleted = "COMPLETED"
	TransactionStatusFailed    = "FAILED"
	TransactionStatusCancelled = "CANCELLED"
)

// PreviousYearPaper represents a previous year exam paper
type PreviousYearPaper struct {
	gorm.Model
	Month    int    `gorm:"not null;check:month >= 1 AND month <= 12"`
	Year     int    `gorm:"not null;check:year >= 1900 AND year <= 2100"`
	PdfUrl   string `gorm:"not null"`
	ExamType string `gorm:"not null;check:exam_type IN ('IIT-JEE', 'NEET')"`
}

// PreviousYearPaperForCreate represents the input for creating a single previous year paper
type PreviousYearPaperForCreate struct {
	Month    int    `json:"month" binding:"required,min=1,max=12"`
	Year     int    `json:"year" binding:"required,min=1900,max=2100"`
	PdfUrl   string `json:"pdf_url" binding:"required"`
	ExamType string `json:"exam_type" binding:"required,oneof=IIT-JEE NEET"`
}

// PreviousYearPapersForCreate represents the input for creating multiple previous year papers
type PreviousYearPapersForCreate struct {
	Papers []PreviousYearPaperForCreate `json:"papers" binding:"required,min=1"`
}

// PreviousYearPapersByExamType represents papers organized by exam type
type PreviousYearPapersByExamType struct {
	ExamType string              `json:"exam_type"`
	Papers   []PreviousYearPaper `json:"papers"`
}

// Transaction represents a course purchase transaction
type Transaction struct {
	gorm.Model
	StudentID        uint      `gorm:"not null"`
	Student          Student   `gorm:"foreignKey:StudentID;constraint:OnDelete:CASCADE"`
	Amount           int       `gorm:"not null"` // Amount in smallest currency unit (e.g., paise)
	Status           string    `gorm:"not null;check:status IN ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED')"`
	TransactionDate  time.Time `gorm:"not null"`
	PaymentMethod    string    // e.g., "UPI", "CARD", "NET_BANKING"
	PaymentReference string    // External payment gateway reference
	Courses          []Course  `gorm:"many2many:transaction_courses;"`
}

// TransactionForCreate represents the input for creating a new transaction
type TransactionForCreate struct {
	CourseIDs     []uint `json:"course_ids" binding:"required"`
	PaymentMethod string `json:"payment_method" binding:"required"`
}

// TransactionStatusUpdate represents the input for updating transaction status
type TransactionStatusUpdate struct {
	Status           string `json:"status" binding:"required,oneof=PENDING COMPLETED FAILED CANCELLED"`
	PaymentReference string `json:"payment_reference,omitempty"`
}

// TransactionSummary represents a simplified transaction for API responses
type TransactionSummary struct {
	ID               uint                  `json:"id"`
	Amount           int                   `json:"amount"`
	Status           string                `json:"status"`
	TransactionDate  time.Time             `json:"transaction_date"`
	PaymentMethod    string                `json:"payment_method"`
	PaymentReference string                `json:"payment_reference,omitempty"`
	Courses          []CourseWithPurchased `json:"courses"`
}

// TransactionHistory represents transaction history for a student
type TransactionHistory struct {
	Transactions []TransactionSummary `json:"transactions"`
	TotalAmount  int                  `json:"total_amount"`
}
